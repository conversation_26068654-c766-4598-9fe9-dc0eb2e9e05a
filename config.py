import os

class Config:
    """应用配置类"""
    
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    # 数据配置
    DATA_DIR = "表格数据"
    
    # AI模型配置
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY', '')
    OPENAI_API_BASE = os.environ.get('OPENAI_API_BASE', 'https://api.openai.com/v1')
    
    # 支持的大模型API配置
    AI_PROVIDERS = {
        'openai': {
            'api_key': OPENAI_API_KEY,
            'base_url': OPENAI_API_BASE,
            'model': 'gpt-3.5-turbo'
        },
        'qianwen': {
            'api_key': os.environ.get('QIANWEN_API_KEY', ''),
            'base_url': 'https://dashscope.aliyuncs.com/api/v1',
            'model': 'qwen-turbo'
        },
        'wenxin': {
            'api_key': os.environ.get('WENXIN_API_KEY', ''),
            'base_url': 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop',
            'model': 'ernie-bot-turbo'
        }
    }
    
    # 默认使用的AI提供商
    DEFAULT_AI_PROVIDER = 'openai'
    
    # 数据类型配置
    DATA_TYPES = {
        'gdp': {
            'name': 'GDP数据',
            'file': 'GDP表.xlsx',
            'main_column': 'GDP',
            'description': 'GDP和GDP增长率数据'
        },
        'stock': {
            'name': '股票市场',
            'file': '股票市场表.xlsx',
            'main_column': 'StockIndex',
            'description': '股票指数和交易量数据'
        },
        'interest': {
            'name': '利率数据',
            'file': '利率表.xlsx',
            'main_column': 'BaseRate',
            'description': '基准利率和货币供应量数据'
        },
        'fiscal': {
            'name': '财政状况',
            'file': '财政状况表.xlsx',
            'main_column': 'BudgetBalance',
            'description': '财政赤字/盈余和公共债务数据'
        },
        'trade': {
            'name': '贸易差额',
            'file': '贸易差额表.xlsx',
            'main_column': 'Exports',
            'description': '出口额和进口额数据'
        },
        'inflation': {
            'name': '通货膨胀',
            'file': '通货膨胀表.xlsx',
            'main_column': 'InflationRate',
            'description': 'CPI和通胀率数据'
        },
        'unemployment': {
            'name': '失业率',
            'file': '失业率表.xlsx',
            'main_column': 'UnemploymentRate',
            'description': '失业率和劳动参与率数据'
        }
    }
