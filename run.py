#!/usr/bin/env python3
"""
智能问数助手启动脚本
"""

import os
import sys
from app import app

def main():
    """主函数"""
    print("=" * 50)
    print("智能问数助手")
    print("基于大模型的智能数据分析平台")
    print("=" * 50)
    
    # 检查数据目录
    data_dir = app.config['DATA_DIR']
    if not os.path.exists(data_dir):
        print(f"错误: 数据目录 '{data_dir}' 不存在")
        sys.exit(1)
    
    # 检查数据文件
    missing_files = []
    for data_type, config in app.config['DATA_TYPES'].items():
        file_path = os.path.join(data_dir, config['file'])
        if not os.path.exists(file_path):
            missing_files.append(config['file'])
    
    if missing_files:
        print("警告: 以下数据文件不存在:")
        for file in missing_files:
            print(f"  - {file}")
        print("系统将使用模拟数据运行")
    
    # 检查AI配置
    ai_configured = False
    for provider, config in app.config['AI_PROVIDERS'].items():
        if config.get('api_key'):
            ai_configured = True
            print(f"AI提供商 '{provider}' 已配置")
            break
    
    if not ai_configured:
        print("提示: 未配置AI API密钥，将使用模拟分析")
        print("可以通过环境变量设置:")
        print("  - OPENAI_API_KEY: OpenAI API密钥")
        print("  - QIANWEN_API_KEY: 通义千问API密钥")
        print("  - WENXIN_API_KEY: 文心一言API密钥")
    
    print("\n启动服务器...")
    print(f"访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)
    
    # 启动Flask应用
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=app.config['DEBUG']
        )
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
