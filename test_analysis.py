#!/usr/bin/env python3
"""
测试智能问数助手的分析功能
"""

import requests
import json
from datetime import datetime, timedelta

def test_analysis_api():
    """测试数据分析API"""
    
    # 测试数据
    test_data = {
        "data_type": "gdp",
        "start_date": "2020-01-01",
        "end_date": "2023-12-01",
        "regions": ["美国", "中国", "日本"]
    }
    
    print("=" * 50)
    print("测试智能问数助手分析功能")
    print("=" * 50)
    
    print(f"测试参数:")
    print(f"  数据类型: {test_data['data_type']}")
    print(f"  时间范围: {test_data['start_date']} 到 {test_data['end_date']}")
    print(f"  地区: {', '.join(test_data['regions'])}")
    print()
    
    try:
        print("发送分析请求...")
        response = requests.post(
            'http://localhost:5000/api/analyze',
            headers={'Content-Type': 'application/json'},
            json=test_data,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("\n✅ 分析成功!")
            print(f"📊 图表数据点数量: {len(result['chart_data'])}")
            print(f"📈 主要分析列: {result['column_name']}")
            
            # 显示数据摘要
            summary = result['summary']
            print(f"\n📋 数据摘要:")
            print(f"  时间范围: {summary['time_range']}")
            print(f"  地区: {', '.join(summary['regions'])}")
            print(f"  平均值: {summary['mean_value']:.2f}")
            print(f"  最大值: {summary['max_value']:.2f}")
            print(f"  最小值: {summary['min_value']:.2f}")
            print(f"  平均同比增长率: {summary['avg_yoy']:.2f}%")
            print(f"  平均环比增长率: {summary['avg_mom']:.2f}%")
            
            # 显示AI分析
            print(f"\n🤖 AI分析:")
            print(result['analysis'])
            
            # 显示部分图表数据
            print(f"\n📊 图表数据样例 (前3条):")
            for i, data_point in enumerate(result['chart_data'][:3]):
                print(f"  {i+1}. {data_point['date']} - {data_point['region']}: {data_point['value']:.2f}")
            
            return True
            
        else:
            print(f"❌ 分析失败!")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保服务器正在运行在 http://localhost:5000")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时! 分析可能需要更长时间")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_different_data_types():
    """测试不同数据类型"""
    
    data_types = ["gdp", "stock", "inflation"]
    
    print("\n" + "=" * 50)
    print("测试不同数据类型")
    print("=" * 50)
    
    for data_type in data_types:
        print(f"\n测试数据类型: {data_type}")
        
        test_data = {
            "data_type": data_type,
            "start_date": "2022-01-01",
            "end_date": "2023-06-01",
            "regions": ["美国", "中国"]
        }
        
        try:
            response = requests.post(
                'http://localhost:5000/api/analyze',
                headers={'Content-Type': 'application/json'},
                json=test_data,
                timeout=20
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"  ✅ {data_type} 分析成功 - 数据点: {len(result['chart_data'])}")
            else:
                print(f"  ❌ {data_type} 分析失败 - {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ {data_type} 测试异常: {e}")

if __name__ == "__main__":
    # 基本功能测试
    success = test_analysis_api()
    
    if success:
        # 测试不同数据类型
        test_different_data_types()
        
        print("\n" + "=" * 50)
        print("🎉 测试完成!")
        print("💡 建议:")
        print("  1. 在浏览器中访问 http://localhost:5000 进行完整测试")
        print("  2. 尝试不同的时间范围和地区组合")
        print("  3. 如需AI分析功能，请配置相应的API密钥")
        print("=" * 50)
    else:
        print("\n❌ 基本测试失败，请检查服务器状态")
