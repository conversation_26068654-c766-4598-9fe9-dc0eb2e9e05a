<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能问数助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .control-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .analysis-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
        .summary-card {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .btn-analyze {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem 2rem;
            font-weight: bold;
        }
        .btn-analyze:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-chart-bar me-3"></i>智能问数助手</h1>
                    <p class="mb-0">基于大模型的智能数据分析平台</p>
                </div>
                <div class="col-md-4 text-end">
                    <i class="fas fa-robot fa-3x"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- 控制面板 -->
        <div class="control-panel">
            <h4><i class="fas fa-cogs me-2"></i>数据查询设置</h4>
            <div class="row">
                <div class="col-md-3">
                    <label for="dataType" class="form-label">数据类型</label>
                    <select class="form-select" id="dataType">
                        <option value="">请选择数据类型</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="startDate" class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="col-md-3">
                    <label for="endDate" class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="endDate">
                </div>
                <div class="col-md-3">
                    <label for="regions" class="form-label">选择地区</label>
                    <select class="form-select" id="regions" multiple>
                        <option value="">请先选择数据类型</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <button class="btn btn-primary btn-analyze" onclick="analyzeData()">
                        <i class="fas fa-search me-2"></i>开始分析
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载动画 -->
        <div class="loading" id="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">分析中...</span>
            </div>
            <p class="mt-3">正在分析数据，请稍候...</p>
        </div>

        <!-- 图表区域 -->
        <div class="chart-container" id="chartContainer" style="display: none;">
            <h4><i class="fas fa-chart-column me-2"></i>数据可视化</h4>
            <canvas id="dataChart" width="400" height="200"></canvas>
        </div>

        <!-- 数据摘要 -->
        <div class="summary-card" id="summaryCard" style="display: none;">
            <h5><i class="fas fa-info-circle me-2"></i>数据摘要</h5>
            <div id="summaryContent"></div>
        </div>

        <!-- AI分析结果 -->
        <div class="analysis-section" id="analysisSection" style="display: none;">
            <h4><i class="fas fa-brain me-2"></i>AI智能分析</h4>
            <div id="analysisContent" class="mt-3"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentChart = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDataTypes();
            setDefaultDates();
        });

        // 设置默认日期
        function setDefaultDates() {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setFullYear(endDate.getFullYear() - 2);
            
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
        }

        // 加载数据类型
        async function loadDataTypes() {
            try {
                const response = await fetch('/api/data-types');
                const dataTypes = await response.json();
                
                const select = document.getElementById('dataType');
                select.innerHTML = '<option value="">请选择数据类型</option>';
                
                for (const [key, value] of Object.entries(dataTypes)) {
                    const option = document.createElement('option');
                    option.value = key;
                    option.textContent = value;
                    select.appendChild(option);
                }
                
                // 监听数据类型变化
                select.addEventListener('change', loadRegions);
            } catch (error) {
                console.error('加载数据类型失败:', error);
                showError('加载数据类型失败');
            }
        }

        // 加载地区选项
        async function loadRegions() {
            const dataType = document.getElementById('dataType').value;
            const regionsSelect = document.getElementById('regions');
            
            if (!dataType) {
                regionsSelect.innerHTML = '<option value="">请先选择数据类型</option>';
                return;
            }
            
            try {
                const response = await fetch(`/api/regions/${dataType}`);
                const regions = await response.json();
                
                regionsSelect.innerHTML = '';
                regions.forEach(region => {
                    const option = document.createElement('option');
                    option.value = region;
                    option.textContent = region;
                    option.selected = true; // 默认选中所有地区
                    regionsSelect.appendChild(option);
                });
            } catch (error) {
                console.error('加载地区失败:', error);
                showError('加载地区失败');
            }
        }

        // 分析数据
        async function analyzeData() {
            const dataType = document.getElementById('dataType').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const regionsSelect = document.getElementById('regions');
            const selectedRegions = Array.from(regionsSelect.selectedOptions).map(option => option.value);

            // 验证输入
            if (!dataType || !startDate || !endDate || selectedRegions.length === 0) {
                showError('请填写完整的查询条件');
                return;
            }

            if (new Date(startDate) >= new Date(endDate)) {
                showError('开始日期必须早于结束日期');
                return;
            }

            // 显示加载动画
            showLoading(true);
            hideResults();

            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        data_type: dataType,
                        start_date: startDate,
                        end_date: endDate,
                        regions: selectedRegions
                    })
                });

                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.error || '分析失败');
                }

                // 显示结果
                displayChart(result.chart_data, result.column_name);
                displaySummary(result.summary);
                displayAnalysis(result.analysis);
                showResults();

            } catch (error) {
                console.error('分析失败:', error);
                showError(error.message || '分析失败，请稍后重试');
            } finally {
                showLoading(false);
            }
        }

        // 显示图表
        function displayChart(chartData, columnName) {
            const ctx = document.getElementById('dataChart').getContext('2d');
            
            // 销毁现有图表
            if (currentChart) {
                currentChart.destroy();
            }

            // 准备数据
            const regions = [...new Set(chartData.map(item => item.region))];
            const dates = [...new Set(chartData.map(item => item.date))].sort();
            
            const datasets = regions.map((region, index) => {
                const regionData = chartData.filter(item => item.region === region);
                const data = dates.map(date => {
                    const item = regionData.find(d => d.date === date);
                    return item ? item.value : null;
                });
                
                const colors = [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)'
                ];
                
                return {
                    label: region,
                    data: data,
                    backgroundColor: colors[index % colors.length],
                    borderColor: colors[index % colors.length].replace('0.8', '1'),
                    borderWidth: 1
                };
            });

            currentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: dates,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: `${columnName} 数据分析`
                        },
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: columnName
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '时间'
                            }
                        }
                    }
                }
            });
        }

        // 显示数据摘要
        function displaySummary(summary) {
            const content = document.getElementById('summaryContent');
            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>时间范围：</strong>${summary.time_range}</p>
                        <p><strong>地区：</strong>${summary.regions.join(', ')}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>平均值：</strong>${summary.mean_value.toFixed(2)}</p>
                        <p><strong>最大值：</strong>${summary.max_value.toFixed(2)}</p>
                        <p><strong>最小值：</strong>${summary.min_value.toFixed(2)}</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>平均同比增长率：</strong>${summary.avg_yoy.toFixed(2)}%</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>平均环比增长率：</strong>${summary.avg_mom.toFixed(2)}%</p>
                    </div>
                </div>
            `;
        }

        // 显示AI分析
        function displayAnalysis(analysis) {
            const content = document.getElementById('analysisContent');
            content.innerHTML = `<div class="alert alert-info">${analysis.replace(/\n/g, '<br>')}</div>`;
        }

        // 显示/隐藏加载动画
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // 显示结果
        function showResults() {
            document.getElementById('chartContainer').style.display = 'block';
            document.getElementById('summaryCard').style.display = 'block';
            document.getElementById('analysisSection').style.display = 'block';
        }

        // 隐藏结果
        function hideResults() {
            document.getElementById('chartContainer').style.display = 'none';
            document.getElementById('summaryCard').style.display = 'none';
            document.getElementById('analysisSection').style.display = 'none';
        }

        // 显示错误信息
        function showError(message) {
            alert('错误: ' + message);
        }
    </script>
</body>
</html>
