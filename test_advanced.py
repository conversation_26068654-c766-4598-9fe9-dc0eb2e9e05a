#!/usr/bin/env python3
"""
高级测试：测试更复杂的分析场景
"""

import requests
import json

def test_advanced_analysis():
    """测试高级分析功能"""
    
    print("=" * 60)
    print("高级分析测试 - 多地区长时间序列分析")
    print("=" * 60)
    
    # 测试更复杂的场景
    test_data = {
        "data_type": "inflation",
        "start_date": "2020-01-01",
        "end_date": "2024-12-01",
        "regions": ["美国", "中国", "日本", "德国", "英国", "法国"]
    }
    
    print(f"📊 测试场景:")
    print(f"  数据类型: 通货膨胀数据")
    print(f"  时间跨度: 5年 (2020-2024)")
    print(f"  地区数量: 6个主要经济体")
    print(f"  预期数据点: ~300个")
    print()
    
    try:
        print("🔄 执行分析...")
        response = requests.post(
            'http://localhost:5000/api/analyze',
            headers={'Content-Type': 'application/json'},
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ 高级分析成功!")
            print(f"📈 数据点总数: {len(result['chart_data'])}")
            print(f"🎯 分析指标: {result['column_name']}")
            
            # 分析数据质量
            chart_data = result['chart_data']
            regions_in_data = set(item['region'] for item in chart_data)
            dates_in_data = set(item['date'] for item in chart_data)
            
            print(f"\n📋 数据质量分析:")
            print(f"  实际地区数: {len(regions_in_data)}")
            print(f"  时间点数: {len(dates_in_data)}")
            print(f"  数据完整性: {len(chart_data)/(len(regions_in_data)*len(dates_in_data))*100:.1f}%")
            
            # 显示各地区数据样例
            print(f"\n🌍 各地区最新数据:")
            latest_data = {}
            for item in chart_data:
                region = item['region']
                if region not in latest_data or item['date'] > latest_data[region]['date']:
                    latest_data[region] = item
            
            for region, data in sorted(latest_data.items()):
                yoy_str = f", 同比: {data['yoy']:.2f}%" if data['yoy'] is not None else ""
                print(f"  {region}: {data['value']:.2f}{yoy_str}")
            
            # 显示AI分析
            summary = result['summary']
            print(f"\n📊 统计摘要:")
            print(f"  平均通胀率: {summary['mean_value']:.2f}%")
            print(f"  最高通胀率: {summary['max_value']:.2f}%")
            print(f"  最低通胀率: {summary['min_value']:.2f}%")
            print(f"  平均同比变化: {summary['avg_yoy']:.2f}%")
            
            print(f"\n🤖 AI深度分析:")
            print(result['analysis'])
            
            return True
            
        else:
            print(f"❌ 分析失败: {response.status_code}")
            print(f"错误详情: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    
    print("\n" + "=" * 60)
    print("边界情况测试")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            "name": "单地区短期分析",
            "data": {
                "data_type": "gdp",
                "start_date": "2023-01-01",
                "end_date": "2023-06-01",
                "regions": ["中国"]
            }
        },
        {
            "name": "多地区股票数据",
            "data": {
                "data_type": "stock",
                "start_date": "2022-01-01",
                "end_date": "2023-01-01",
                "regions": ["美国", "中国", "日本", "德国"]
            }
        },
        {
            "name": "失业率长期趋势",
            "data": {
                "data_type": "unemployment",
                "start_date": "2020-01-01",
                "end_date": "2024-01-01",
                "regions": ["美国", "英国"]
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}: {test_case['name']}")
        
        try:
            response = requests.post(
                'http://localhost:5000/api/analyze',
                headers={'Content-Type': 'application/json'},
                json=test_case['data'],
                timeout=20
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"  ✅ 成功 - 数据点: {len(result['chart_data'])}, 指标: {result['column_name']}")
            else:
                print(f"  ❌ 失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")

if __name__ == "__main__":
    # 高级分析测试
    success = test_advanced_analysis()
    
    if success:
        # 边界情况测试
        test_edge_cases()
        
        print("\n" + "=" * 60)
        print("🎊 所有高级测试完成!")
        print("✨ 系统表现优秀，可以处理各种复杂分析场景")
        print("🚀 建议在浏览器中进行交互式测试以获得最佳体验")
        print("=" * 60)
    else:
        print("\n❌ 高级测试失败，请检查系统状态")
