# 智能问数助手

基于大模型的智能数据分析平台，支持多种经济金融数据的可视化分析和AI洞察生成。

## 功能特性

- 📊 **多数据类型支持**: GDP、股票市场、利率、财政状况、贸易差额、通货膨胀、失业率
- 📈 **可视化图表**: 基于Chart.js的交互式柱状图展示
- 🤖 **AI智能分析**: 集成大模型API，自动生成数据洞察和趋势分析
- 📊 **同比环比计算**: 自动计算年同比(YoY)和月环比(MoM)增长率
- 🌍 **多地区对比**: 支持多个国家/地区的数据对比分析
- 📱 **响应式设计**: 适配桌面和移动设备

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API      │    │   大模型API     │
│  (HTML/JS/CSS)  │◄──►│  (Flask)       │◄──►│  (OpenAI/其他)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   数据处理层    │
                       │  (Pandas/NumPy) │
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Excel数据文件 │
                       │  (表格数据目录)  │
                       └─────────────────┘
```

## 快速开始

### 1. 环境准备

确保已安装Python 3.8+：

```bash
python --version
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置AI API（可选）

设置环境变量来配置AI分析功能：

```bash
# Windows
set OPENAI_API_KEY=your_openai_api_key_here

# Linux/Mac
export OPENAI_API_KEY=your_openai_api_key_here
```

支持的AI提供商：
- **OpenAI**: 设置 `OPENAI_API_KEY`
- **通义千问**: 设置 `QIANWEN_API_KEY`
- **文心一言**: 设置 `WENXIN_API_KEY`

### 4. 启动应用

```bash
python run.py
```

### 5. 访问应用

打开浏览器访问: http://localhost:5000

## 数据格式

系统支持以下数据类型，数据文件应放在 `表格数据/` 目录下：

### GDP数据 (GDP表.xlsx)
- 时间 (Date)
- 国家或地区 (Region)
- GDP
- GDP增长率 (GrowthRate)

### 股票市场 (股票市场表.xlsx)
- 时间 (Date)
- 国家或地区 (Region)
- 股票指数 (StockIndex)
- 交易量 (TradingVolume)

### 通货膨胀 (通货膨胀表.xlsx)
- 时间 (Date)
- 国家或地区 (Region)
- CPI
- 通胀率 (InflationRate)

*其他数据类型格式请参考 `表格数据/字段说明.txt`*

## 使用说明

1. **选择数据类型**: 从下拉菜单选择要分析的数据类型
2. **设置时间范围**: 选择开始和结束日期
3. **选择地区**: 多选要对比的国家或地区
4. **开始分析**: 点击"开始分析"按钮
5. **查看结果**: 
   - 查看关键指标卡片
   - 分析可视化图表
   - 阅读AI生成的分析报告

## API接口

### 获取数据类型
```
GET /api/data-types
```

### 获取地区列表
```
GET /api/regions/<data_type>
```

### 数据分析
```
POST /api/analyze
Content-Type: application/json

{
    "data_type": "gdp",
    "start_date": "2020-01-01",
    "end_date": "2024-12-01",
    "regions": ["中国", "美国", "日本"]
}
```

## 配置说明

主要配置在 `config.py` 文件中：

- `DATA_DIR`: 数据文件目录
- `AI_PROVIDERS`: AI提供商配置
- `DATA_TYPES`: 支持的数据类型配置

## 技术栈

- **后端**: Python Flask
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **数据处理**: Pandas, NumPy
- **图表库**: Chart.js
- **UI框架**: Bootstrap 5
- **AI集成**: OpenAI API, 通义千问, 文心一言

## 开发说明

### 项目结构
```
├── app.py              # Flask应用主文件
├── config.py           # 配置文件
├── run.py              # 启动脚本
├── requirements.txt    # Python依赖
├── templates/          # HTML模板
│   └── index.html     # 主页面
├── 表格数据/           # 数据文件目录
│   ├── GDP表.xlsx
│   ├── 股票市场表.xlsx
│   └── ...
└── README.md          # 说明文档
```

### 扩展新的数据类型

1. 在 `config.py` 的 `DATA_TYPES` 中添加配置
2. 在 `DataProcessor` 类中添加列名映射
3. 准备对应的Excel数据文件

### 扩展新的AI提供商

1. 在 `config.py` 的 `AI_PROVIDERS` 中添加配置
2. 在 `AIAnalyzer` 类中实现对应的API调用方法

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
