from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timedelta
import requests
from typing import Dict, List, Any, Optional
from config import Config

app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# 配置
DATA_DIR = app.config['DATA_DIR']

class DataProcessor:
    """数据处理类"""

    def __init__(self):
        self.data_types = app.config['DATA_TYPES']

        # 列名映射（中文到英文）
        self.column_mapping = {
            'gdp': {'时间': 'Date', '国家或地区': 'Region', 'GDP': 'GDP', 'GDP增长率': 'GrowthRate'},
            'stock': {'时间': 'Date', '国家或地区': 'Region', '股票指数': 'StockIndex', '交易量': 'TradingVolume'},
            'interest': {'时间': 'Date', '国家或地区': 'Region', '基准利率': 'BaseRate', '总货币供应量': 'MoneySupply'},
            'fiscal': {'时间': 'Date', '国家或地区': 'Region', '财政赤字/盈余': 'BudgetBalance', '公共债务': 'PublicDebt'},
            'trade': {'时间': 'Date', '国家或地区': 'Region', '出口额': 'Exports', '进口额': 'Imports'},
            'inflation': {'时间': 'Date', '国家或地区': 'Region', 'CPI': 'CPI', '通胀率': 'InflationRate'},
            'unemployment': {'时间': 'Date', '国家或地区': 'Region', '失业率': 'UnemploymentRate', '劳动参与率': 'LaborParticipation'}
        }

    def load_data(self, data_type: str) -> pd.DataFrame:
        """加载指定类型的数据"""
        if data_type not in self.data_types:
            raise ValueError(f"不支持的数据类型: {data_type}")

        file_path = os.path.join(DATA_DIR, self.data_types[data_type]['file'])
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"数据文件不存在: {file_path}")

        try:
            df = pd.read_excel(file_path)

            # 重命名列为英文
            if data_type in self.column_mapping:
                df = df.rename(columns=self.column_mapping[data_type])

            # 确保日期列格式正确
            if 'Date' in df.columns:
                df['Date'] = pd.to_datetime(df['Date'])

            return df
        except Exception as e:
            print(f"读取文件失败: {e}")
            raise e
    
    def get_main_value_column(self, data_type: str) -> str:
        """获取主要数值列名"""
        if data_type in self.data_types:
            return self.data_types[data_type]['main_column']
        return 'value'
    
    def filter_data(self, df: pd.DataFrame, start_date: str, end_date: str, 
                   regions: List[str] = None) -> pd.DataFrame:
        """根据时间区间和地区过滤数据"""
        # 时间过滤
        mask = (df['Date'] >= start_date) & (df['Date'] <= end_date)
        filtered_df = df.loc[mask]
        
        # 地区过滤
        if regions:
            filtered_df = filtered_df[filtered_df['Region'].isin(regions)]
            
        return filtered_df
    
    def calculate_yoy_mom(self, df: pd.DataFrame, value_column: str) -> pd.DataFrame:
        """计算同比和环比"""
        df = df.sort_values(['Region', 'Date'])
        
        # 计算同比 (Year over Year)
        df['YoY'] = df.groupby('Region')[value_column].pct_change(periods=12) * 100
        
        # 计算环比 (Month over Month)
        df['MoM'] = df.groupby('Region')[value_column].pct_change(periods=1) * 100
        
        return df

class AIAnalyzer:
    """AI分析类"""
    
    def __init__(self):
        self.providers = app.config['AI_PROVIDERS']
        self.default_provider = app.config['DEFAULT_AI_PROVIDER']
        
    def analyze_data(self, data_summary: Dict[str, Any], data_type: str) -> str:
        """使用大模型分析数据"""
        provider_config = self.providers.get(self.default_provider)

        if not provider_config or not provider_config.get('api_key'):
            return self._generate_mock_analysis(data_summary, data_type)

        prompt = self._create_analysis_prompt(data_summary, data_type)

        try:
            if self.default_provider == 'openai':
                return self._call_openai_api(prompt, provider_config)
            else:
                # 可以扩展支持其他AI提供商
                return self._generate_mock_analysis(data_summary, data_type)

        except Exception as e:
            print(f"AI分析失败: {e}")
            return self._generate_mock_analysis(data_summary, data_type)

    def _call_openai_api(self, prompt: str, config: Dict[str, str]) -> str:
        """调用OpenAI API"""
        response = requests.post(
            f"{config['base_url']}/chat/completions",
            headers={
                'Authorization': f"Bearer {config['api_key']}",
                'Content-Type': 'application/json'
            },
            json={
                'model': config['model'],
                'messages': [
                    {'role': 'system', 'content': '你是一个专业的数据分析师，擅长分析经济和金融数据。'},
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': 500,
                'temperature': 0.7
            }
        )

        if response.status_code == 200:
            return response.json()['choices'][0]['message']['content']
        else:
            raise Exception(f"API调用失败: {response.status_code}")
    
    def _create_analysis_prompt(self, data_summary: Dict[str, Any], data_type: str) -> str:
        """创建分析提示词"""
        return f"""
        请分析以下{data_type}数据：
        
        数据概要：
        - 时间范围：{data_summary.get('time_range', '')}
        - 地区：{', '.join(data_summary.get('regions', []))}
        - 平均值：{data_summary.get('mean_value', 0):.2f}
        - 最大值：{data_summary.get('max_value', 0):.2f}
        - 最小值：{data_summary.get('min_value', 0):.2f}
        - 同比增长率：{data_summary.get('avg_yoy', 0):.2f}%
        - 环比增长率：{data_summary.get('avg_mom', 0):.2f}%
        
        请从以下角度进行分析：
        1. 数据趋势分析
        2. 同比环比变化的原因
        3. 可能的影响因素
        4. 未来趋势预测
        
        请用中文回答，控制在300字以内。
        """
    
    def _generate_mock_analysis(self, data_summary: Dict[str, Any], data_type: str) -> str:
        """生成模拟分析（当API不可用时）"""
        avg_yoy = data_summary.get('avg_yoy', 0)
        avg_mom = data_summary.get('avg_mom', 0)
        
        trend = "上升" if avg_yoy > 0 else "下降"
        
        return f"""
        基于当前{data_type}数据分析：
        
        1. 趋势分析：数据整体呈现{trend}趋势，同比增长率为{avg_yoy:.2f}%，环比增长率为{avg_mom:.2f}%。
        
        2. 变化特征：{'数据表现积极，显示出良好的增长势头' if avg_yoy > 0 else '数据出现下滑，需要关注潜在风险'}。
        
        3. 影响因素：可能受到宏观经济政策、市场环境变化、季节性因素等多重影响。
        
        4. 趋势预测：建议持续关注相关指标变化，{'预期未来可能继续保持增长态势' if avg_yoy > 0 else '需要采取相应措施应对下行压力'}。
        """

# 初始化组件
data_processor = DataProcessor()
ai_analyzer = AIAnalyzer()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/data-types')
def get_data_types():
    """获取可用的数据类型"""
    data_types = {key: value['name'] for key, value in app.config['DATA_TYPES'].items()}
    return jsonify(data_types)

@app.route('/api/regions/<data_type>')
def get_regions(data_type):
    """获取指定数据类型的可用地区"""
    try:
        df = data_processor.load_data(data_type)
        regions = df['Region'].unique().tolist()
        return jsonify(regions)
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/analyze', methods=['POST'])
def analyze_data():
    """分析数据并返回结果"""
    try:
        data = request.json
        data_type = data.get('data_type')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        regions = data.get('regions', [])
        
        # 加载和过滤数据
        df = data_processor.load_data(data_type)
        filtered_df = data_processor.filter_data(df, start_date, end_date, regions)
        
        if filtered_df.empty:
            return jsonify({'error': '没有找到符合条件的数据'}), 400
        
        # 获取主要分析列
        main_column = data_processor.get_main_value_column(data_type)
        if main_column not in filtered_df.columns:
            return jsonify({'error': f'数据中缺少主要分析列: {main_column}'}), 400
        
        # 计算同比环比
        df_with_ratios = data_processor.calculate_yoy_mom(filtered_df, main_column)
        
        # 准备图表数据
        chart_data = []
        for _, row in df_with_ratios.iterrows():
            chart_data.append({
                'date': row['Date'].strftime('%Y-%m'),
                'region': row['Region'],
                'value': float(row[main_column]),
                'yoy': float(row['YoY']) if not pd.isna(row['YoY']) else None,
                'mom': float(row['MoM']) if not pd.isna(row['MoM']) else None
            })
        
        # 数据摘要
        data_summary = {
            'time_range': f"{start_date} 至 {end_date}",
            'regions': regions,
            'mean_value': float(filtered_df[main_column].mean()),
            'max_value': float(filtered_df[main_column].max()),
            'min_value': float(filtered_df[main_column].min()),
            'avg_yoy': float(df_with_ratios['YoY'].mean()) if not df_with_ratios['YoY'].isna().all() else 0,
            'avg_mom': float(df_with_ratios['MoM'].mean()) if not df_with_ratios['MoM'].isna().all() else 0
        }
        
        # AI分析
        analysis = ai_analyzer.analyze_data(data_summary, data_type)
        
        return jsonify({
            'chart_data': chart_data,
            'summary': data_summary,
            'analysis': analysis,
            'column_name': main_column
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
